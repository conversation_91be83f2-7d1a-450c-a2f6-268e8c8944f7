import { useState, useCallback, useEffect } from 'react';
import {
  useCategoriesApi,
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from 'src/services/api/categories-api';
import { CategoryFormValues } from '../form/category-schema';

// Helper functions to convert between form data and API data
const convertFormToApiRequest = (
  formData: CategoryFormValues
): CreateCategoryRequest | UpdateCategoryRequest => {
  return {
    name: formData.name,
    description: formData.description,
    icon: formData.icon,
    theme: formData.colorType === 'custom' ? formData.customColor || '#7D40D9' : formData.colorType,
  };
};

const convertApiToFormData = (category: Category): CategoryFormValues => {
  // Check if theme is a hex color (custom) or a predefined color type
  const isCustomColor = category.theme.startsWith('#');

  return {
    name: category.name,
    description: category.description,
    icon: category.icon,
    colorType: isCustomColor ? 'custom' : (category.theme as any),
    customColor: isCustomColor ? category.theme : undefined,
  };
};

const convertApiCategoryToDisplayCategory = (apiCategory: Category): Category => {
  // Convert API category to display category with additional UI properties
  const isCustomColor = apiCategory.theme.startsWith('#');

  return {
    ...apiCategory,
    colorType: isCustomColor ? 'custom' : (apiCategory.theme as any),
    customColor: isCustomColor ? apiCategory.theme : undefined,
    agentsCount: apiCategory.agentsCount || 0,
  };
};

// This is a custom hook that combines the API services with local state management
export const useCategoriesView = () => {
  // State for categories
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Get the API hooks
  const { useGetCategories, useCreateCategory, useUpdateCategory, useDeleteCategory } =
    useCategoriesApi();

  // API hooks
  const { mutate: createCategory } = useCreateCategory((data) => {
    // Add the new category to the local state
    const displayCategory = convertApiCategoryToDisplayCategory(data);
    setCategories((prev) => [...prev, displayCategory]);
    handleCloseDialog();
  });

  const { mutate: updateCategory } = useUpdateCategory(selectedCategory?.id || 0, () => {
    // Refresh the categories list
    fetchCategories();
    handleCloseDialog();
  });

  const { mutate: deleteCategory } = useDeleteCategory(() => {
    // Refresh the categories list
    fetchCategories();
  });

  // Get categories data from the API
  const { data: categoriesResponse, isLoading, isError, refetch } = useGetCategories();

  // Update local state when API data changes
  useEffect(() => {
    if (categoriesResponse?.categories) {
      // Convert API categories to display categories
      const displayCategories = categoriesResponse.categories.map(
        convertApiCategoryToDisplayCategory
      );
      console.log('displayCategories ', displayCategories);
      setCategories(displayCategories);
      setLoading(false);
      setError(null);
    }
  }, [categoriesResponse]);

  // Update loading state
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  // Update error state
  useEffect(() => {
    if (isError) {
      setError('Failed to fetch categories');
    }
  }, [isError]);

  // Fetch categories function for manual refetching
  const fetchCategories = useCallback(() => {
    setLoading(true);
    refetch();
  }, [refetch]);

  // For development, use mock data if API call fails
  useEffect(() => {
    if (isError && import.meta.env.DEV) {
      console.log('Using mock data for categories');
      setLoading(false);
      setError(null);
    }
  }, [isError]);

  // Initial fetch
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Dialog handlers
  const handleOpenDialog = useCallback((category?: Category) => {
    setSelectedCategory(category || null);
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedCategory(null);
  }, []);

  // Open the dialog to create a new category
  const handleOpenCreateDialog = useCallback(() => {
    setSelectedCategory(null);
    setOpenDialog(true);
  }, []);

  // Handle creating a new category
  const handleCreateCategory = useCallback(
    (data: CategoryFormValues) => {
      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data);

      // Use the mutation from the API hook
      createCategory(apiRequest, {
        onError: (err) => {
          console.error('Failed to create category:', err);

          // For development, simulate success with mock data
          if (import.meta.env.DEV) {
            const newCategory = {
              id: categories.length + 1,
              name: data.name,
              description: data.description,
              icon: data.icon,
              theme: data.colorType === 'custom' ? data.customColor || '#7D40D9' : data.colorType,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              colorType: data.colorType,
              customColor: data.customColor,
              agentsCount: 0,
            } as Category;

            setCategories((prev) => [...prev, newCategory]);
            handleCloseDialog();
          }
        },
      });
    },
    [categories, createCategory, handleCloseDialog]
  );

  // Handle updating a category
  const handleUpdateCategory = useCallback(
    (data: CategoryFormValues) => {
      if (!selectedCategory) return;

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data);

      // Use the mutation from the API hook
      updateCategory(apiRequest, {
        onError: (err) => {
          console.error('Failed to update category:', err);

          // For development, simulate success with mock data
          if (import.meta.env.DEV) {
            const updatedCategory = {
              ...selectedCategory,
              name: data.name,
              description: data.description,
              icon: data.icon,
              theme: data.colorType === 'custom' ? data.customColor || '#7D40D9' : data.colorType,
              updatedAt: new Date().toISOString(),
              colorType: data.colorType,
              customColor: data.customColor,
            };

            setCategories((prev) =>
              prev.map((cat) => (cat.id === selectedCategory.id ? updatedCategory : cat))
            );
            handleCloseDialog();
          }
        },
      });
    },
    [selectedCategory, updateCategory, handleCloseDialog]
  );

  // Handle deleting a category
  const handleDeleteCategory = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

      // Use the mutation from the API hook
      deleteCategory(numericId, {
        onError: (err) => {
          console.error('Failed to delete category:', err);

          // For development, simulate success with mock data
          if (import.meta.env.DEV) {
            setCategories((prev) => prev.filter((cat) => cat.id !== numericId));
          }
        },
      });
    },
    [deleteCategory]
  );

  // Handle editing a category
  const handleEditCategory = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
      const category = categories.find((cat) => cat.id === numericId);
      if (category) {
        // Convert API category to form data for editing
        const formData = convertApiToFormData(category);
        handleOpenDialog({ ...category, ...formData } as Category);
      }
    },
    [categories, handleOpenDialog]
  );

  return {
    // State
    categories,
    loading,
    error,
    openDialog,
    selectedCategory,

    // Handlers
    handleOpenDialog,
    handleCloseDialog,
    handleCreateCategory,
    handleUpdateCategory,
    handleDeleteCategory,
    handleEditCategory,
    handleOpenCreateDialog,

    // Refetch
    refetch: fetchCategories,
  };
};
