import type { AxiosRequestConfig } from 'axios';

import axios from 'axios';

import { CONFIG } from 'src/config-global';
import { paths } from 'src/routes/paths';

// ----------------------------------------------------------------------

// Callback for handling 401 errors - to be set by auth context
let onUnauthorizedCallback: (() => void) | null = null;

export const setUnauthorizedCallback = (callback: (() => void) | null) => {
  onUnauthorizedCallback = callback;
};

const axiosInstance = axios.create({ baseURL: CONFIG.site.WorkforcesServerUrl });

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log('erorr', error);
    const currentEndPoint = error.response.data.path;
    console.log('response path hereee', error.response.data.path);
    if (
      error.response?.status === 401 &&
      !(
        currentEndPoint !== endpoints.auth.refreshToken || currentEndPoint !== endpoints.auth.signIn
      )
    ) {
      onUnauthorizedCallback?.();
    }
    // Always throw the backend error message (or fallback)
    const errorMessage =
      error.response?.data?.message || error.response?.statusText || 'Something went wrong!';

    return Promise.reject(new Error(errorMessage));
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  try {
    const [url, config] = Array.isArray(args) ? args : [args];

    const res = await axiosInstance.get(url, { ...config });

    return res.data;
  } catch (error) {
    console.error('Failed to fetch:', error);
    throw error;
  }
};

// ----------------------------------------------------------------------

export const endpoints = {
  auth: {
    signIn: '/auth/login/admin',
    signUp: '/auth/register',
    signOut: '/auth/logout',
    refreshToken: '/auth/refresh-token',
    forgetPassword: '/auth/request-password-change',
    resetPassword: '/auth/reset-password',
    exchangeOAuthCode: '/auth/login/exchange-code',
  },
  user: {
    me: '/users/me',
  },
};
